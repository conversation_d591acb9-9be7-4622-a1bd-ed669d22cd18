/* CSS Custom Properties for better maintainability */
:root {
  --primary-color: #1585cf;
  --text-color: #000;
  --bg-color: #fff;
  --border-color: #ddd;
  --font-family: "Open Sans", Arial, sans-serif;
  --font-size-base: 1.6rem;
  --font-size-large: 1.7rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
}

/* Dark mode variables */
[data-theme="dark"] {
  --text-color: #f0f0f0;
  --bg-color: #121212;
  --border-color: #333;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
  color: var(--text-color);
  font-weight: 500;
  margin: 0 0 var(--spacing-md);
}

p, table, tr, td, ul, li, blockquote, nav, a, footer, dl, dt, dd {
  font-size: var(--font-size-large);
  font-family: var(--font-family);
  font-weight: 400;
}

dt {
  font-weight: 600;
}

p {
  line-height: 1.5;
  margin-bottom: 0.9rem;
  text-align: justify;
}

blockquote {
  border-left-color: slategray;
  color: slategray;
}

blockquote blockquote {
  border-left: none;
  margin: 1.5rem;
  font-style: italic;
}

cite {
  font-size: 1.5rem;
  display: block;
  margin-top: var(--spacing-sm);
}

blockquote footer {
  margin-top: 1.5rem;
  margin-left: var(--spacing-md);
  text-indent: -1.8rem;
}

/* Links */
#main-container a {
  color: var(--primary-color);
  font-weight: 500;
  transition: color 0.2s ease;
}

#main-container a:hover {
  color: #0f6ba8;
  text-decoration: none;
}

/* Main title styling */
.main-title {
  margin-top: var(--spacing-lg);
  text-align: left;
}

/* Footer controls */
.footer-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.dark-mode-toggle {
  background: none;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode-toggle:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.dark-mode-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Profile section */
.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.headshot-image {
  width: 100%;
  max-width: 250px;
  height: auto;
  border-radius: 8px;
  border: 3px solid var(--border-color);
  margin-bottom: var(--spacing-md);
  transition: border-color 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-table {
  width: 100%;
  max-width: 250px;
  border: none;
  margin: 0;
}

.contact-table td {
  border: none;
  padding: 0.5rem 0;
  text-align: left;
  vertical-align: top;
}

.contact-table td:first-child {
  width: 30px;
  text-align: center;
  color: var(--text-color);
}

iframe {
  padding: 3px;
  border: 1px solid var(--border-color);
}

/* Header styling with underline effect */
h1, h2, h3 {
  position: relative;
}

h1 span, h2 span, h3 span {
  background-color: var(--bg-color);
  padding-right: 10px;
  transition: background-color 0.3s ease;
}

h1:after, h2:after, h3:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.5em;
  border-top: 2px solid var(--text-color);
  z-index: -1;
  transition: border-color 0.3s ease;
}

/* Footer */
footer {
  padding-bottom: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.social-icons {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.social-icons li {
  padding-left: var(--spacing-md);
  display: inline-block;
}

.social-icons li a {
  color: var(--primary-color);
  transition: color 0.2s ease;
}

.social-icons li a:hover {
  color: #0f6ba8;
}

/* Panel and accordion styles (if needed for other pages) */
#links .panel-default {
  border-radius: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

#links .panel-heading {
  border-bottom: none;
}

#links img {
  margin-right: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  width: 100px;
  padding: 3px;
  border: 1px solid var(--border-color);
}

dd {
  margin-bottom: var(--spacing-sm);
}

.list-group li a {
  display: block;
}

/* Accordion styles */
.panel-heading .accordion-plus-toggle:before,
.panel-heading .accordion-arrow-toggle:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  float: right;
  color: var(--border-color);
}

.panel-heading .accordion-plus-toggle:before {
  content: "\f068";
}

.panel-heading .accordion-plus-toggle.collapsed:before {
  content: "\f067";
}

.panel-heading .accordion-arrow-toggle:before {
  content: "\f078";
}

.panel-heading .accordion-arrow-toggle.collapsed:before {
  content: "\f054";
}

.panel-title > a {
  display: block;
}

#badge-more {
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
}

/* Automatic dark mode detection (fallback) */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --text-color: #f0f0f0;
    --bg-color: #121212;
    --border-color: #333;
  }
}

/* Responsive Design */
@media (max-width: 767px) {
  .main-title {
    margin-top: var(--spacing-md);
    font-size: 2rem;
    text-align: center;
  }

  .profile-section {
    margin-bottom: var(--spacing-md);
  }

  .headshot-image {
    max-width: 200px;
  }

  .contact-table {
    max-width: 200px;
  }

  .contact-table td {
    font-size: 1.5rem;
    padding: 0.3rem 0;
  }

  p {
    font-size: 1.5rem;
    text-align: left;
  }

  .footer-controls {
    justify-content: center;
    margin-top: var(--spacing-sm);
  }

  footer .row {
    text-align: center;
  }

  footer .col-sm-6:first-child {
    margin-bottom: var(--spacing-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 1200px;
  }

  .profile-section {
    align-items: flex-start;
  }

  .headshot-image,
  .contact-table {
    max-width: 100%;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 1000px;
  }
}