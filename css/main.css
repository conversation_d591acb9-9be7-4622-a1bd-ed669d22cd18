body {
  font-size: 16px;
  font-family: "Open Sans", <PERSON>l, sans-serif;
  background: #FFFFFF;
}

.dark-blue {
  color: #20364c !important;
}

h1, h2, h3, h4, h5, h6 {
  font-family: "Open Sans", Helvetica, sans-serif;
  color: black;
  font-weight: 500;
}

h1, h2, h3 {
  margin: 0 0 2rem !important;
}

.panel-heading h5 {
  font-style: italic;
  font-weight: 500;
  color: #1585cf !important;
}

p, table, tr, td, ul, li, blockquote, nav, a, footer, dl, dt, dd {
  text-align: justify;
  font-size: 1.7rem;
  font-family: "Open Sans", Helvetica, sans-serif;
  font-weight: 400;
}

dt {
  font-weight: 600;
}

blockquote {
  border-left-color: slategray;
  color: slategray;
}

blockquote blockquote {
  border-left: none;
  margin: 1.5rem;
  font-style: italic;
}

cite {
  font-size: 1.5rem !important;
  display: block;
  margin-top: 1rem;
}

p {
  line-height: 1.5;
  margin-bottom: 0.9rem;
}

blockquote footer {
  margin-top: 1.5rem;
  margin-left: 2rem;
  text-indent: -1.8rem;
}

#main-container a {
  color: #1585cf;
  font-weight: 500;
}

#title {
  color: black;
  margin-top: 2rem;
  font-weight: 600;
  font-size: 1.7rem;
  text-align: center;
}

#program {
  text-align: center;
}

iframe {
  padding: 3px;
  border: 1px solid lightgray;
}

.padding-left-1 {
  padding-left: 1rem !important;
}

.padding-left-2 {
  padding-left: 2rem !important;
}

.push-down-1 {
  margin-top: 1rem !important;
}

.push-down-2 {
  margin-top: 2rem !important;
}

.push-down-3 {
  margin-top: 3rem !important;
}

.push-down-4 {
  margin-top: 4rem !important;
}

.pull-up-1 {
  margin-top: -1rem !important;
}

.pull-up-2 {
  margin-top: -1rem !important;
}

.pull-up-3 {
  margin-top: -1rem !important;
}

.spacer-div-3 {
  height: 45px !important;
}

h1, h2, h3 {
  position: relative;
}

h1 span, h2 span, h3 span {
  background-color: white;
  padding-right: 10px;
}

h1:after, h2:after, h3:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.5em;
  border-top: 2px solid black;
  z-index: -1;
}

footer {
  padding-bottom: 2rem;
}

.social-icons {
  list-style-type: none;
}

.social-icons li {
  padding-left: 2rem;
  display: inline-block;
}

.social-icons li a {
  color: #1585cf;
}

#links .panel-default {
  border-radius: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

#links .panel-heading {
  border-bottom: none;
}

#links img {
  margin-right: 3rem;
  margin-bottom: 2rem;
  width: 100px;
  padding: 3px;
  border: 1px solid silver;
}

dd {
  margin-bottom: 1rem;
}

.list-group li a {
  display: block;
}

.panel-heading .accordion-plus-toggle:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f068";
  float: right;
  color: silver;
}

.panel-heading .accordion-plus-toggle.collapsed:before {
  content: "\f067";
  color: silver;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.panel-heading .accordion-arrow-toggle:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f078";
  float: right;
  color: silver;
}

.panel-heading .accordion-arrow-toggle.collapsed:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f054";
  color: silver;
}

.panel-title>a {
  display: block;
}

#badge-more {
  background-color: #1585cf;
  color: white;
  cursor: pointer;
}

@media (prefers-color-scheme: dark) {
  body, h1 span, h2 span, h3 span {
    background-color: #121212;
    color: #f0f0f0;
  }
  
  h1:after, h2:after, h3:after {
    border-top: 2px solid #f0f0f0;
  }
  
}

@media only screen and (min-width: 767px) {
  .container {
    max-width: 730px;
  }
}

@media only screen and (min-width: 768px) {
  .container {
    max-width: 1200px;
  }
}