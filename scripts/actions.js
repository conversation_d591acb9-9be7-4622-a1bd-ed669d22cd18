// Dark mode functionality
function initDarkMode() {
    const toggle = document.getElementById('dark-mode-toggle');
    const icon = toggle.querySelector('i');

    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    let currentTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

    // Apply initial theme
    applyTheme(currentTheme);
    updateToggleIcon(icon, currentTheme);

    // Toggle functionality
    toggle.addEventListener('click', () => {
        currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
        applyTheme(currentTheme);
        updateToggleIcon(icon, currentTheme);
        localStorage.setItem('theme', currentTheme);
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            currentTheme = e.matches ? 'dark' : 'light';
            applyTheme(currentTheme);
            updateToggleIcon(icon, currentTheme);
        }
    });
}

function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
}

function updateToggleIcon(icon, theme) {
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// Map functionality
function map() {
    Swal.fire({
        confirmButtonText: "<div style='font-size: 18px; width: 150px'><i class='fa fa-map-marked-alt'></i>&nbsp;Campus map<div>",
        confirmButtonColor: "#1585cf",
        showDenyButton: true,
        denyButtonColor: "#1585cf",
        denyButtonText: "<div style='font-size: 18px; width: 150px'><i class='fa fa-building'></i>&nbsp;Floor map<div>"
    }).then((result) => {
        if (result.isConfirmed) {
            window.open("https://tinyurl.com/SDSUSSE")
        } else if (result.isDenied) {
            Swal.fire({
                imageUrl: "img/SSE_map.jpg",
                width: "100%",
                confirmButtonColor: "#1585cf"
            })
        }
    })
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initDarkMode);