function map() {
    Swal.fire({
        confirmButtonText: "<div style='font-size: 18px; width: 150px'><i class='fa fa-map-marked-alt'></i>&nbsp;Campus map<div>",
        confirmButtonColor: "#1585cf",
        showDenyButton: true,
        denyButtonColor: "#1585cf",
        denyButtonText: "<div style='font-size: 18px; width: 150px'><i class='fa fa-building'></i>&nbsp;Floor map<div>"
    }).then((result) => {
        if (result.isConfirmed) {
            window.open("https://tinyurl.com/SDSUSSE")
        } else if (result.isDenied) {
            swal.fire({
                imageUrl: "img/SSE_map.jpg",
                width: "100%",
                confirmButtonColor: "#1585cf"
            })
        }
    })
}